networks:
  traefik:
    external: true
  internal:
    driver: bridge
    name: internal-prod

volumes:
  mysql_data_prod:
  redis_data_prod:
  minio_data_prod:
  loki_index_prod:
  loki_index_cache_prod:
  grafana_data_prod:
  traefik_data:
  traefik_logs_prod: {}       # Shared log volume for Traefik access logs
  promtail_positions_prod: {} # Positions file for Promtail

services:
  # Traefik Reverse Proxy (Production)
  traefik:
    image: traefik:v3.5
    container_name: traefik_prod
    restart: unless-stopped
    command:
      - --api.dashboard=true
      - --api.debug=false
      - --log.level=INFO
      - --providers.docker=true
      - --providers.docker.exposedbydefault=false
      - --providers.docker.network=traefik
      - --entrypoints.web.address=:80
      - --entrypoints.websecure.address=:443
      - --entrypoints.web8002.address=:8002
      - --entrypoints.web8003.address=:8003
      - --entrypoints.web8004.address=:8004
      - --entrypoints.web8005.address=:8005
      - --certificatesresolvers.letsencrypt.acme.tlschallenge=true
      - --certificatesresolvers.letsencrypt.acme.email=${ACME_EMAIL}
      - --certificatesresolvers.letsencrypt.acme.storage=/letsencrypt/acme.json
      - --metrics.prometheus=true
      # --- Access log to shared file, in JSON (for Promtail to parse) ---
      - --accesslog=true
      - --accesslog.filepath=/var/log/traefik/access.json
      - --accesslog.format=json
    ports:
      - "80:80"
      - "443:443"
      - "8002:8002"  # Storage (MinIO API)
      - "8003:8003"  # MinIO Console
      - "8004:8004"  # Monitoring (Grafana)
      - "8005:8005"  # API (NestJS)
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - traefik_data:/letsencrypt
      - traefik_logs_prod:/var/log/traefik
    networks:
      - traefik
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.traefik.rule=Host(`${DOMAIN}`) && PathPrefix(`/traefik`)"
      - "traefik.http.routers.traefik.entrypoints=websecure"
      - "traefik.http.routers.traefik.tls.certresolver=letsencrypt"
      - "traefik.http.routers.traefik.service=api@internal"
      - "traefik.http.routers.traefik.middlewares=auth,traefik-stripprefix"
      - "traefik.http.middlewares.auth.basicauth.users=${TRAEFIK_AUTH}"
      - "traefik.http.middlewares.traefik-stripprefix.stripprefix.prefixes=/traefik"
      - "traefik.http.routers.traefik.observability.accesslogs=false"
      # Global redirect to https
      - "traefik.http.routers.http-catchall.rule=hostregexp(`{host:.+}`)"
      - "traefik.http.routers.http-catchall.entrypoints=web"
      - "traefik.http.routers.http-catchall.middlewares=redirect-to-https"
      - "traefik.http.middlewares.redirect-to-https.redirectscheme.scheme=https"

  # MySQL Database
  mysql:
    image: mysql:8.4
    container_name: omo_mysql_prod
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}
      MYSQL_DATABASE: ${DB_NAME}
      MYSQL_USER: ${DB_USER}
      MYSQL_PASSWORD: ${DB_PASSWORD}
    volumes:
      - mysql_data_prod:/var/lib/mysql
      - ./omo-shopify-app/init.sql:/docker-entrypoint-initdb.d
    networks:
      - internal
    command: >
      --innodb-buffer-pool-size=${MAX_MEMORY_MYSQL:-1G}
      --innodb-redo-log-capacity=536870912
      --max-connections=200
      --innodb-flush-log-at-trx-commit=2
      --innodb-flush-method=O_DIRECT
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${DB_ROOT_PASSWORD}"]
      timeout: 10s
      retries: 10
      interval: 30s

  # Redis Cache & Queue
  redis:
    image: redis:7-alpine
    container_name: omo_redis_prod
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory ${MAX_MEMORY_REDIS:-512M} --maxmemory-policy allkeys-lru --save 900 1 --save 300 10
    volumes:
      - redis_data_prod:/data
    networks:
      - internal
    healthcheck:
      test: ["CMD-SHELL", "redis-cli ping | grep PONG"]
      interval: 10s
      timeout: 3s
      retries: 5

  # MinIO Object Storage
  minio:
    image: quay.io/minio/minio:latest
    container_name: omo_minio_prod
    restart: unless-stopped
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD}
    volumes:
      - minio_data_prod:/data
    networks:
      - internal
      - traefik
    labels:
      - "traefik.enable=true"
      # MinIO API
      - "traefik.http.routers.minio-api.rule=PathPrefix(`/`)"
      - "traefik.http.routers.minio-api.entrypoints=web8002"
      - "traefik.http.routers.minio-api.service=minio-api"
      - "traefik.http.services.minio-api.loadbalancer.server.port=9000"
      # MinIO Console
      - "traefik.http.routers.minio-console.rule=PathPrefix(`/`)"
      - "traefik.http.routers.minio-console.entrypoints=web8003"
      - "traefik.http.routers.minio-console.service=minio-console"
      - "traefik.http.services.minio-console.loadbalancer.server.port=9001"

  # MinIO Bucket Initialization
  minio-mc-init:
    image: minio/mc:latest
    depends_on:
      - minio
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD}
    networks:
      - internal
    entrypoint: >
      /bin/sh -c "
      until (mc alias set local http://minio:9000 $$MINIO_ROOT_USER $$MINIO_ROOT_PASSWORD) do sleep 1; done &&
      mc mb -p local/loki || true &&
      mc mb -p local/backups || true
      "
    restart: "no"

  # Loki Volume Initialization
  loki-init:
    image: alpine:latest
    container_name: loki_init_prod
    volumes:
      - loki_index_prod:/loki/index
      - loki_index_cache_prod:/loki/index_cache
    command: >
      sh -c "
      mkdir -p /loki/index/uploader /loki/index_cache &&
      chmod -R 777 /loki/index /loki/index_cache &&
      chown -R 10001:10001 /loki/index /loki/index_cache || true
      "
    restart: "no"

  # Loki Log Aggregation
  loki:
    image: grafana/loki:latest
    container_name: omo_loki_prod
    restart: unless-stopped
    depends_on:
      - minio
      - minio-mc-init
      - loki-init
    command: ["-config.file=/etc/loki/loki-config.yml", "-config.expand-env=true"]
    environment:
      # S3/MinIO Configuration (Required)
      LOKI_S3_ENDPOINT: http://minio:9000
      LOKI_S3_BUCKET: loki
      LOKI_S3_REGION: us-east-1
      AWS_ACCESS_KEY_ID: ${MINIO_ROOT_USER}
      AWS_SECRET_ACCESS_KEY: ${MINIO_ROOT_PASSWORD}
      # Optional Configuration (uses defaults from loki-config.yml if not set)
      LOKI_HTTP_PORT: 3100
      LOKI_PATH_PREFIX: /loki
      LOKI_INSTANCE_ADDR: 127.0.0.1
      LOKI_KVSTORE: inmemory
      LOKI_REPLICATION_FACTOR: 1
      LOKI_SCHEMA_FROM: "2024-01-01"
      LOKI_INDEX_STORE: tsdb
      LOKI_OBJECT_STORE: s3
      LOKI_SCHEMA_VERSION: v13
      LOKI_INDEX_PREFIX: index_
      LOKI_INDEX_PERIOD: 24h
      LOKI_INDEX_DIRECTORY: /loki/index
      LOKI_CACHE_DIRECTORY: /loki/index_cache
      LOKI_S3_FORCE_PATH_STYLE: true
      LOKI_S3_INSECURE: true
    volumes:
      - loki_index_prod:/loki/index
      - loki_index_cache_prod:/loki/index_cache
      - ./loki-config.yml:/etc/loki/loki-config.yml:ro
    networks:
      - internal
    user: "10001"

  # Promtail: tails Traefik logs and pushes to Loki
  promtail:
    image: grafana/promtail:2.9.8
    container_name: omo_promtail_prod
    restart: unless-stopped
    depends_on:
      - loki
      - traefik
    command: ["-config.file=/etc/promtail/promtail-config.yml"]
    volumes:
      - traefik_logs_prod:/var/log/traefik:ro
      - ./promtail-config.yml:/etc/promtail/promtail-config.yml:ro
      - promtail_positions_prod:/positions
    networks:
      - internal
      - traefik

  # Grafana Monitoring
  grafana:
    image: grafana/grafana:latest
    container_name: omo_grafana_prod
    restart: unless-stopped
    depends_on:
      - loki
    environment:
      GF_SECURITY_ADMIN_USER: ${GRAFANA_USER}
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD}
      GF_SERVER_ROOT_URL: http://localhost:8004
      GF_SECURITY_COOKIE_SECURE: "true"
      GF_SECURITY_STRICT_TRANSPORT_SECURITY: "true"
    volumes:
      - grafana_data_prod:/var/lib/grafana
      - ./provisioning/datasources/datasource.yml:/etc/grafana/provisioning/datasources/datasource.yml:ro
    networks:
      - internal
      - traefik
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.grafana.rule=PathPrefix(`/`)"
      - "traefik.http.routers.grafana.entrypoints=web8004"
      - "traefik.http.routers.grafana.observability.accesslogs=false"
      - "traefik.http.services.grafana.loadbalancer.server.port=3000"

  # OMO Shopify App
  omo-shopify-app:
    build:
      context: ./omo-shopify-app
      dockerfile: Dockerfile
    container_name: omo_shopify_app_prod
    restart: unless-stopped
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
      loki:
        condition: service_started
    environment:
      NODE_ENV: production
      DATABASE_URL: mysql://${DB_USER}:${DB_PASSWORD}@mysql:3306/${DB_NAME}
      SHOPIFY_API_KEY: ${SHOPIFY_API_KEY}
      SHOPIFY_API_SECRET: ${SHOPIFY_API_SECRET}
      SHOPIFY_APP_URL: https://app.${DOMAIN}
      SCOPES: ${SHOPIFY_SCOPES}
      SYSTEM_TOKEN: ${SYSTEM_TOKEN}
      API_VERSION: ${SHOPIFY_API_VERSION}
      PORT: 3000
      REDIS_HOST: redis
      REDIS_PORT: 6379
      LOKI_URL: http://loki:3100
    volumes:
      - ./omo-shopify-app/logs:/app/logs
    networks:
      - internal
      - traefik
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.shopify-app.rule=Host(`app.${DOMAIN}`)"
      - "traefik.http.routers.shopify-app.entrypoints=websecure"
      - "traefik.http.routers.shopify-app.tls.certresolver=letsencrypt"
      - "traefik.http.services.shopify-app.loadbalancer.server.port=3000"
      - "traefik.http.routers.shopify-app.middlewares=security-headers"
      - "traefik.http.middlewares.security-headers.headers.customrequestheaders.X-Forwarded-Proto=https"
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # OMO NestJS Backend
  omo-nest-be:
    build:
      context: ./omo-nest-be
      dockerfile: Dockerfile
    container_name: omo_nest_be_prod
    restart: unless-stopped
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
      loki:
        condition: service_started
    environment:
      NODE_ENV: production
      DATABASE_URL: mysql://${DB_USER}:${DB_PASSWORD}@mysql:3306/${DB_NAME}
      REDIS_URL: redis://redis:6379
      PORT: 3000
      DB_HOST: mysql
      DB_PORT: 3306
      DB_USERNAME: ${DB_USER}
      DB_PASSWORD: ${DB_PASSWORD}
      DB_DATABASE: ${DB_NAME}
      REDIS_HOST: redis
      REDIS_PORT: 6379
      LOKI_URL: http://loki:3100
    networks:
      - internal
      - traefik
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.nest-api.rule=PathPrefix(`/`)"
      - "traefik.http.routers.nest-api.entrypoints=web8005"
      - "traefik.http.services.nest-api.loadbalancer.server.port=3000"
      - "traefik.http.routers.nest-api.middlewares=api-cors-prod"
      - "traefik.http.middlewares.api-cors-prod.headers.accesscontrolallowmethods=GET,OPTIONS,PUT,POST,DELETE,PATCH"
      - "traefik.http.middlewares.api-cors-prod.headers.accesscontrolalloworiginlist=http://localhost:8005,https://app.${DOMAIN}"
      - "traefik.http.middlewares.api-cors-prod.headers.accesscontrolmaxage=100"
      - "traefik.http.middlewares.api-cors-prod.headers.addvaryheader=true"
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
